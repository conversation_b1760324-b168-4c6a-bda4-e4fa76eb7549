[paths]
wishlist = ./wanted.txt
save_directory = ./captures

[settings]
checkInterval = 20

# (OPTIONAL) - leave blank if you dont want to run a post processing script on the files
# You can set a command to be ran on the file once it is completed. This can be any sort of a script you would like.
# You can create a script to convert the video via ffmpeg to make it compatible for certain devices, create a contact sheet of the video
# upload the video to a cloud storage drive via rclone, or whatever else you see fit.
# set the string to be the same as you you would type into terminal to call the script manually.
# The peramaters which will be passed to the script are as follows:
# 1 = full file path (ie: /Users/<USER>/cam4/hannah/2017.07.26_19.34.47_hannah.mp4)
# 2 = filename (ie : 2017.07.26_19.34.47_hannah.mp4)
# 3 = directory (ie : /Users/<USER>/cam4/hannah/)
# 4 = models name (ie: hannah)
# 5 = filename without the extension (ie: 2017.07.26_19.34.47_hannah)
# 6 = 'cam4' - thats it, just 'cam4' to identify the site.
# to call a bash script called "MoveToGoogleDrive.sh" and located in the user Joes home directory, you would use:
# postProcessingCommand = "bash /Users/<USER>/home/<USER>"
# this script will be ran on the files "download location" prior to it being moved to its "completed location".
# The moving of the file will not take place if a post processing script is ran, so if you want to move it once it is completed, do so through commands in the post processing script.

postProcessingCommand =


# Because depending on what the post processing script does, it may be demanding on the system.
# Set the maximum number of concurrent post processing scripts you would like to be ran at one time.
# (required if using a post processing script)

postProcessingThreads =

[proxy]
; Uncomment and provide proxy details if needed
url = http://127.0.0.1:7890
; username = your-username
; password = your-password